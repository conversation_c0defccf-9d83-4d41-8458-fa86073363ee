# OxyGent中文how-to指南
> 本系列文档将指导您使用OxyGent逐步搭建MAS系统
> 如果您是MAS框架的新用户，建议您按顺序阅读文档，尤其是标注*的部分

文档的现有教程包括：

## 安装和启动
+ [安装OxyGent](./0_install.md)*
+ [运行demo](./0_1_demo.md)*

## 核心功能
+ [创建第一个智能体](./1_register_single_agent.md)*
+ [和智能体交流](./1_1_chat_with_agent.md)
+ [选择智能体使用的LLM](./1_2_select_llm.md)
+ [预设提示词](./1_3_select_prompt.md)
+ [选择智能体种类](./1_4_select_agent.md)

## 工具
+ [注册一个工具](./2_register_single_tool.md)*
+ [使用MCP开源工具](./2_3_use_opensource_tools.md)*
+ [使用MCP自定义工具](./2_4_use_mcp_tools.md)*
+ [管理工具调用](./2_2_manage_tools.md)

## 设置
+ [设置OxyGent Config](./3_set_config.md)*
+ [设置数据库](./3_1_set_database.md)

## 多智能体系统
+ [创建简单的多agent系统](./6_register_multi_agent.md)*
+ [复制相同智能体](./6_1_moa.md)
+ [并行调用agent](./7_parallel.md)*
+ [提供响应元数据](./8_1_trust_mode.md)
+ [处理查询和提示词](./8_update_prompts.md)*
+ [处理LLM和智能体输出](./8_2_handle_output.md)
+ [反思重做模式](./8_3_reflexion.md)

## 流
+ [创建工作流](./9_workflow.md)*
+ [创建流](./9_2_preset_flow.md)
+ [获取记忆和重新生成](./9_1_continue_exec.md)
+ [创建分布式系统](./11_dstributed.md)*

## 多模态
+ [使用多模态智能体](./10_multimodal.md)*

## 动态模型功能
+ [检索增强生成(RAG)](./12_rag.md)*
+ [生成训练样本](./13_training.md)*

## 调试
+ [可视化界面调试](./14_debugging.md)