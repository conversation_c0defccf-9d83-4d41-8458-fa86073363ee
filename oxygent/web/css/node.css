* {
    box-sizing: border-box;
}
body {
    min-height: 100vh;
    background-color: #F3F3F5;
}

::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: #f5f5f5;
}

 ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.3);
}

 ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4); /* 鼠标悬停时的滚动条颜色 */
}

.node-warpper {
    /* background-color: #F3F3F5; */
}

/* 顶部吸顶 */
.node-container-top{
    position: sticky;
    top: 0;
    background-color: #F3F3F5;
    z-index: 999;
}


.node-container {
    display: flex;
    flex-direction: row;
    width: 100vw;
    padding: 10px;
}

.node-left {
    box-sizing: border-box;
    overflow: auto;
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
    margin-left: 10px;
    padding: 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.node-right {
    background-color: #fff;
    /*height: calc(100vh - 100px);*/
    width: 580px;
    margin-left: 20px;
    margin-right: 20px;
    overflow: auto;
    border-radius: 4px;
    padding: 18px 20px;
}

.node-right .right_top {
    margin-bottom: 20px;
}

.collapse-card {
    /*background-color: #ffffff;*/
    background: #F3F3F5 url('../image/bg-img.svg') repeat left top;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
}



/*node 页面*/
.agentCard {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    border-radius: 4px;
    /* padding: 6px; */
    background-color: #F3F3F5;
    padding:10px 20px;
}

.avatarContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 14.2857px;
    box-sizing: border-box;
    width: 60px;
    height: 60px;
    /*background-color: rgb(254, 234, 213);*/
    border-radius: 4px;
}

.avatarEmoji img {
    width: 60px;
    height: 60px;
    border-radius: 4px;
}

.contentWrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    /*height: 65px;*/
    color: rgb(0, 0, 0);
}

.titleContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    /*width: 170px;*/
    height: 25px;
}

.agentName {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.39;
}

.tagContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    min-width: 60px;
    height: 25px;
    background-color: rgb(225, 239, 253);
    border-radius: 99px;
}

.tagText {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.42;
}

.metaInfo {
    /*opacity: 0.5;*/
    color: #ACACAC;
    max-width: 473px;
    font-size: 14px;
    line-height: 1;
}

.ellipsisText {
    display: -webkit-box;
    overflow: hidden;
    word-break: break-all;
    webkit-line-clamp: 2;
    webkit-box-orient: vertical;
}

.mod-header {
    display: flex;
    justify-content: space-between;
}


.mod-header-operation-btn {
    border: none;
    outline: none;
    cursor: pointer;
    margin: 0;
    font-family: inherit;
    background: none;
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #E4E4E4;
}

.mod-container {
    display: flex;
    width: 100%;
    margin-top: 5px;
}

.mod-left {
    display: flex;
    flex-direction: column;
    /* gap: 20px; */
    min-width: 350px;
    /*flex: 1 1 300px;*/
    /*height: 748px;*/
    font-weight: 500;
    color: rgb(0, 0, 0);
    border-right: 1px solid #EAEAEA;
    box-sizing: border-box;;
    /* padding: 10px; */
    padding-right: 10px;
}

.mod-right {
    margin-left: 10px;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 20px;
    /*width: 100%;*/
    /*height: 748px;*/
    font-weight: 500;
    color: rgb(0, 0, 0);
}

.mod-header-operation {
    margin-top: -10px;
}


.cnt {
    display: flex;
    align-items: center;
    gap: 10px;
    /*width: 100%;*/
    height: 39px;
}

/* .cnt_col:first-child {

} */


.icon {
    width: 39px;
    height: 39px;
}

.text_x_6 {
    font-size: 14px;
    line-height: 1.5;
    color: rgb(0, 0, 0);
}

.text_x_8_wrap {
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    /*width: 270px;*/
    height: 40px;
    padding-left: 10px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #eaeaea;
    border-radius: 2px;
}

.text_x_8 {
    font-size: 14px;
    line-height: 1.5;
    color: rgb(0, 0, 0);
    width: 100%;
    /* 移除边框 */
    border: none;
    /* 移除轮廓线（focus时的外边框） */
    outline: none;
    /* 移除背景色 */
    background: none;
    /* 重置内边距 */
    padding: 5px;
    /* 重置外边距 */
    margin: 0;
}

.image_s_12 {
    width: 142px;
    height: 26px;
}

.section_wrap {
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    height: 40px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #eaeaea;
    border-radius: 2px;
}

.section-input {
    width: 100px;
    /* 移除边框 */
    border: none;
    /* 移除轮廓线（focus时的外边框） */
    outline: none;
    /* 移除背景色 */
    background: none;
    /* 重置内边距 */
    padding: 0;
    /* 重置外边距 */
    margin: 0;
}

.image_s_18 {
    width: 142px;
    height: 26px;
}

.section_wrap1 {
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    width: 57px;
    height: 40px;
    padding-left: 10px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #eaeaea;
    border-radius: 2px;
}

.cnt-switch {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    min-width: 270px;
    height: 36px;
}


.range_s_cnt {
    width: 142px;
}

.section_wrap2 {
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    width: 93px;
    height: 40px;
    padding-left: 10px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #eaeaea;
    border-radius: 2px;
}

.section {
    font-size: 16px;
    line-height: 1.5;
    color: rgb(0, 0, 0);
}

.cnt_col {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    /*width: 270px;*/
    height: 71px;
}
.cnt_col:first-child {
    margin-top: 0;
}


.mod-right-cnt_col {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 98%;
    /*height: 71px;*/
}

.mod-right-text_x_8_wrap {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    padding-left: 10px;
    min-height: 40px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #eaeaea;
    border-radius: 2px;
}


.styled-textarea {
    width: 100%;
    min-height: 300px;
    border: 0;
}


/* Switch 容器 */
.switch {
    position: relative;
    display: inline-block;
    width: 38px;
    height: 24px;
}

/* 隐藏默认的 checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* 滑块样式 */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 1px;
    bottom: 0px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

/* 当 checkbox 被选中时的样式 */
input:checked + .slider {
    background-color: #5048E5;
}

input:focus + .slider {
    box-shadow: 0 0 1px #5048E5;
}

input:checked + .slider:before {
    transform: translateX(13px);
}

/* 禁用状态 */
input:disabled + .slider {
    background-color: #e0e0e0;
    cursor: not-allowed;
}

input:disabled + .slider:before {
    background-color: #f5f5f5;
}


#node-node-map {
    position: relative;
    padding:0 20px;
}

#flowchartId {
    height: 200px;
    overflow: scroll;
}


#node-node-map .title {
    /*margin-top: 10px;*/
    font-size: 18px;
    /*height: 25px;*/
}


.node-node-map-row {
    display: flex;
    gap: 10px;
    align-items: center;
    height: 56px;
}


.node-node-map-row-col-btn {
    cursor: pointer;

}

li {
    list-style: none;
}


.tabs {
    display: flex;
    gap: 4px;
    box-sizing: border-box;
    width: 109px;
    height: 27px;
    padding-top: 3px;
    padding-left: 3px;
    background-color: white;
    border-radius: 4px;
    /*font-family: PingFang SC;*/
    color: black;
}

.tab-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    width: 49px;
    height: 21px;
    border-radius: 2px;
}

/*.tab-item:first-child {*/
/*  background-color: #f3f3f5;*/
/*}*/
/*.tab-item:last-child {*/
/*  width: 50px;*/
/*  background-color: white;*/
/*}*/
.tab-text-active {
    background-color: #f3f3f5;
    color: black;
}

.tab-text {
    font-size: 12px;
    line-height: 1.42;
    color: black;
}

.tab-text-active {
    font-weight: 600;
}


#chart_log_close_id {
    position: absolute;
    right: 10px;
    top: 10px;
}


/*input 滑块支持*/
.slider-container-input {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
}
.slider-container-input-title {
    font-size: 14px;
    line-height: 1.5;
    color: rgb(0, 0, 0);
}

.slider-wrapper-input {
    width: 150px;
    position: relative;
}

.slider-input {
    width: 100%;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    outline: none;
    z-index: 2;
    position: relative;
}

/* 轨道背景（未滑动部分） */
.slider-track-input {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 8px;
    background: #ddd;
    border-radius: 4px;
    z-index: 1;
}

/* 已滑动部分（蓝色） */
.slider-fill-input {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    height: 8px;
    background: #5048E5; /* 蓝色 */
    border-radius: 4px;
    z-index: 1;
    width: 50%; /* 初始值 */
}

/* 滑块样式 - 白色小方块 */
.slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px; /* 方块宽度 */
    height: 16px; /* 方块高度 */
    background: white; /* 白色 */
    cursor: pointer;
    border: 2px solid white; /* 蓝色边框 */
    border-radius: 2px; /* 小圆角 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    z-index: 3;
    position: relative;
}


.slider-input-input {
    display: flex;
    align-items: center;
}

.slider-input-input input {
    width: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    margin-right: 5px;
}

.slider-input-input span {
    font-size: 14px;
    color: #666;
}

