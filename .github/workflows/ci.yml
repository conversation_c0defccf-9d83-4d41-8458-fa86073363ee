name: Oxygent CI

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: ["3.10"]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest playwright pytest-asyncio
        pip install ruff docformatter
        pip install -r requirements.txt

    - name: Format code with Ruff (auto-fix)
      run: |
        ruff format .

    - name: Run Unit Tests
      run: |
        pytest test/unittest
